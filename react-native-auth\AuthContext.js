import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AuthService from './AuthService';

// Auth Context
const AuthContext = createContext();

// Auth Actions
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  RESTORE_TOKEN: 'RESTORE_TOKEN',
  SET_LOADING: 'SET_LOADING',
  SET_BIOMETRIC_INFO: 'SET_BIOMETRIC_INFO',
  BIOMETRIC_LOGIN_START: 'BIOMETRIC_LOGIN_START',
  BIOMETRIC_LOGIN_SUCCESS: 'BIOMETRIC_LOGIN_SUCCESS',
  BIOMETRIC_LOGIN_FAILURE: 'BIOMETRIC_LOGIN_FAILURE',
};

// Initial State
const initialState = {
  isLoading: true,
  isAuthenticated: false,
  user: null,
  accessToken: null,
  error: null,
  biometric: {
    isAvailable: false,
    isEnabled: false,
    biometricType: null,
    biometricTypeName: 'Biometric Authentication',
    isLoading: false,
    error: null,
  },
};

// Auth Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload.user,
        accessToken: action.payload.accessToken,
        error: null,
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        accessToken: null,
        error: action.payload.error,
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        accessToken: null,
        error: null,
      };

    case AUTH_ACTIONS.RESTORE_TOKEN:
      return {
        ...state,
        isLoading: false,
        isAuthenticated: action.payload.isAuthenticated,
        user: action.payload.user,
        accessToken: action.payload.accessToken,
      };

    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };

    case AUTH_ACTIONS.SET_BIOMETRIC_INFO:
      return {
        ...state,
        biometric: {
          ...state.biometric,
          ...action.payload,
        },
      };

    case AUTH_ACTIONS.BIOMETRIC_LOGIN_START:
      return {
        ...state,
        biometric: {
          ...state.biometric,
          isLoading: true,
          error: null,
        },
      };

    case AUTH_ACTIONS.BIOMETRIC_LOGIN_SUCCESS:
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload.user,
        accessToken: action.payload.accessToken,
        error: null,
        biometric: {
          ...state.biometric,
          isLoading: false,
          error: null,
        },
      };

    case AUTH_ACTIONS.BIOMETRIC_LOGIN_FAILURE:
      return {
        ...state,
        biometric: {
          ...state.biometric,
          isLoading: false,
          error: action.payload.error,
        },
      };

    default:
      return state;
  }
};

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Restore authentication state on app start
  useEffect(() => {
    const restoreAuthState = async () => {
      try {
        const isAuthenticated = await AuthService.isAuthenticated();
        const biometricInfo = await AuthService.getBiometricInfo();

        // Set biometric info
        dispatch({
          type: AUTH_ACTIONS.SET_BIOMETRIC_INFO,
          payload: biometricInfo,
        });

        if (isAuthenticated) {
          const user = await AuthService.getUserData();
          const accessToken = await AuthService.getAccessToken();

          dispatch({
            type: AUTH_ACTIONS.RESTORE_TOKEN,
            payload: {
              isAuthenticated: true,
              user,
              accessToken,
            },
          });
        } else {
          dispatch({
            type: AUTH_ACTIONS.RESTORE_TOKEN,
            payload: {
              isAuthenticated: false,
              user: null,
              accessToken: null,
            },
          });
        }
      } catch (error) {
        console.error('Error restoring auth state:', error);
        dispatch({
          type: AUTH_ACTIONS.RESTORE_TOKEN,
          payload: {
            isAuthenticated: false,
            user: null,
            accessToken: null,
          },
        });
      }
    };

    restoreAuthState();
  }, []);

  // Login function
  const login = async (email, password) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      const result = await AuthService.login(email, password);

      if (result.success) {
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user: result.user,
            accessToken: result.accessToken,
          },
        });
        return { success: true };
      } else {
        dispatch({
          type: AUTH_ACTIONS.LOGIN_FAILURE,
          payload: { error: result.error },
        });
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = 'An unexpected error occurred';
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: errorMessage },
      });
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = async () => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
    
    try {
      await AuthService.logout();
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, clear local state
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    }
  };

  // Biometric login function
  const loginWithBiometric = async () => {
    dispatch({ type: AUTH_ACTIONS.BIOMETRIC_LOGIN_START });

    try {
      const result = await AuthService.loginWithBiometric();

      if (result.success) {
        dispatch({
          type: AUTH_ACTIONS.BIOMETRIC_LOGIN_SUCCESS,
          payload: {
            user: result.user,
            accessToken: result.accessToken,
          },
        });
        return { success: true };
      } else {
        dispatch({
          type: AUTH_ACTIONS.BIOMETRIC_LOGIN_FAILURE,
          payload: { error: result.error },
        });
        return {
          success: false,
          error: result.error,
          fallbackToPassword: result.fallbackToPassword
        };
      }
    } catch (error) {
      const errorMessage = 'Biometric authentication failed';
      dispatch({
        type: AUTH_ACTIONS.BIOMETRIC_LOGIN_FAILURE,
        payload: { error: errorMessage },
      });
      return { success: false, error: errorMessage, fallbackToPassword: true };
    }
  };

  // Enable biometric authentication
  const enableBiometric = async (email, password) => {
    try {
      const result = await AuthService.enableBiometric(email, password);

      if (result.success) {
        // Update biometric info
        const biometricInfo = await AuthService.getBiometricInfo();
        dispatch({
          type: AUTH_ACTIONS.SET_BIOMETRIC_INFO,
          payload: biometricInfo,
        });
      }

      return result;
    } catch (error) {
      return { success: false, error: 'Failed to enable biometric authentication' };
    }
  };

  // Disable biometric authentication
  const disableBiometric = async () => {
    try {
      const result = await AuthService.disableBiometric();

      if (result.success) {
        // Update biometric info
        const biometricInfo = await AuthService.getBiometricInfo();
        dispatch({
          type: AUTH_ACTIONS.SET_BIOMETRIC_INFO,
          payload: biometricInfo,
        });
      }

      return result;
    } catch (error) {
      return { success: false, error: 'Failed to disable biometric authentication' };
    }
  };

  // Refresh biometric info
  const refreshBiometricInfo = async () => {
    try {
      const biometricInfo = await AuthService.getBiometricInfo();
      dispatch({
        type: AUTH_ACTIONS.SET_BIOMETRIC_INFO,
        payload: biometricInfo,
      });
      return biometricInfo;
    } catch (error) {
      console.error('Error refreshing biometric info:', error);
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({
      type: AUTH_ACTIONS.LOGIN_FAILURE,
      payload: { error: null },
    });
  };

  // Clear biometric error
  const clearBiometricError = () => {
    dispatch({
      type: AUTH_ACTIONS.BIOMETRIC_LOGIN_FAILURE,
      payload: { error: null },
    });
  };

  // Context value
  const value = {
    // State
    isLoading: state.isLoading,
    isAuthenticated: state.isAuthenticated,
    user: state.user,
    accessToken: state.accessToken,
    error: state.error,
    biometric: state.biometric,

    // Actions
    login,
    logout,
    clearError,

    // Biometric Actions
    loginWithBiometric,
    enableBiometric,
    disableBiometric,
    refreshBiometricInfo,
    clearBiometricError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext;
