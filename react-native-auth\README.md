# React Native Authentication with Refresh Token Flow

This is a complete React Native authentication system with automatic token refresh functionality.

## Features

✅ **Complete Authentication Flow**
- Login with email/password
- Automatic token refresh when access token expires
- Secure token storage using AsyncStorage
- Context-based state management
- Protected API calls with automatic retry

✅ **Security Features**
- Axios interceptors for automatic token attachment
- Failed request queuing during token refresh
- Secure token storage
- Automatic logout on refresh failure

✅ **User Experience**
- Loading states and error handling
- Automatic navigation based on auth state
- Pull-to-refresh functionality
- Clean, modern UI design

## File Structure

```
react-native-auth/
├── AuthService.js          # Core authentication service
├── AuthContext.js          # React Context for auth state
├── SecureApiClient.js      # Secure API client with interceptors
├── LoginScreen.js          # Login screen component
├── HomeScreen.js           # Protected home screen
├── App.js                  # Main app with navigation
├── package.json            # Dependencies
└── README.md              # This file
```

## Installation

1. **Install dependencies:**
```bash
npm install
# or
yarn install
```

2. **Install additional dependencies for Expo:**
```bash
npx expo install @react-native-async-storage/async-storage
npx expo install @react-navigation/native @react-navigation/stack
npx expo install react-native-screens react-native-safe-area-context
npx expo install react-native-gesture-handler react-native-reanimated
```

## Configuration

1. **Update API Base URL:**
   - Open `AuthService.js` and `SecureApiClient.js`
   - Replace `https://your-api-base-url.com/api` with your actual API URL

2. **Backend API Endpoints Required:**
   - `POST /auth/login` - Login endpoint
   - `POST /auth/refresh` - Token refresh endpoint
   - `POST /auth/logout` - Logout endpoint (optional)
   - `GET /user/profile` - Get user profile (example)

## Usage

### 1. AuthService
The main authentication service handles:
- Login/logout operations
- Token storage and retrieval
- Token refresh logic
- Axios interceptor setup

```javascript
import AuthService from './AuthService';

// Login
const result = await AuthService.login(email, password);

// Check authentication
const isAuth = await AuthService.isAuthenticated();

// Logout
await AuthService.logout();
```

### 2. AuthContext
React Context for managing authentication state:

```javascript
import { useAuth } from './AuthContext';

const MyComponent = () => {
  const { isAuthenticated, user, login, logout, isLoading } = useAuth();
  
  // Use auth state and methods
};
```

### 3. SecureApiClient
Secure API client with automatic token refresh:

```javascript
import SecureApiClient from './SecureApiClient';

// Make authenticated API calls
const result = await SecureApiClient.get('/user/profile');
const postResult = await SecureApiClient.post('/data', { key: 'value' });
```

## How Token Refresh Works

1. **Request Interceptor:**
   - Automatically adds access token to all requests
   - Retrieves token from AsyncStorage

2. **Response Interceptor:**
   - Detects 401 (Unauthorized) responses
   - Automatically attempts token refresh
   - Queues failed requests during refresh
   - Retries original request with new token
   - Handles refresh failures by logging out user

3. **Token Refresh Flow:**
   ```
   API Request → 401 Response → Refresh Token → New Access Token → Retry Request
   ```

## API Response Format

Your backend should return tokens in this format:

```javascript
// Login response
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  }
}

// Refresh response
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..." // optional new refresh token
}
```

## Security Considerations

1. **Token Storage:** Uses AsyncStorage (consider react-native-keychain for production)
2. **HTTPS Only:** Always use HTTPS in production
3. **Token Expiry:** Implement proper token expiry times
4. **Refresh Token Rotation:** Consider rotating refresh tokens

## Customization

1. **Add More Screens:** Add registration, forgot password screens
2. **Enhanced Security:** Use react-native-keychain for token storage
3. **Biometric Auth:** Add fingerprint/face ID authentication
4. **Push Notifications:** Add push notification token management

## Testing

Test the authentication flow:

1. **Login:** Enter credentials and verify token storage
2. **API Calls:** Make authenticated requests
3. **Token Refresh:** Wait for token expiry or manually expire token
4. **Logout:** Verify tokens are cleared

## Troubleshooting

1. **Token Refresh Loop:** Check your refresh endpoint and token format
2. **Storage Issues:** Verify AsyncStorage permissions
3. **Navigation Issues:** Ensure proper navigation setup
4. **API Errors:** Check network connectivity and API endpoints

## Production Checklist

- [ ] Replace AsyncStorage with react-native-keychain
- [ ] Add proper error logging
- [ ] Implement biometric authentication
- [ ] Add network connectivity checks
- [ ] Set up proper SSL pinning
- [ ] Add token encryption
- [ ] Implement proper logout on app backgrounding
