# React Native Authentication with Biometric & Refresh Token Flow

This is a complete React Native authentication system with biometric authentication and automatic token refresh functionality.

## Features

✅ **Complete Authentication Flow**
- Login with email/password
- **Biometric authentication (Face ID, Touch ID, Fingerprint)**
- Automatic token refresh when access token expires
- Secure token storage using Keychain/Keystore
- Context-based state management
- Protected API calls with automatic retry

✅ **Biometric Authentication**
- **Cross-platform biometric support (iOS & Android)**
- **Face ID, Touch ID, and Fingerprint authentication**
- **Secure credential storage in device Keychain/Keystore**
- **Automatic fallback to password authentication**
- **Comprehensive error handling with user-friendly messages**
- **Biometric settings management**

✅ **Security Features**
- Axios interceptors for automatic token attachment
- Failed request queuing during token refresh
- **Enhanced secure storage with biometric protection**
- Automatic logout on refresh failure
- **Storage migration between AsyncStorage and Keychain**

✅ **User Experience**
- Loading states and error handling
- Automatic navigation based on auth state
- Pull-to-refresh functionality
- **Biometric prompt with customizable UI**
- **Settings screen for biometric preferences**
- Clean, modern UI design

## File Structure

```
react-native-auth/
├── AuthService.js              # Core authentication service with biometric support
├── AuthContext.js              # React Context for auth state management
├── BiometricService.js         # Biometric authentication service
├── BiometricErrorHandler.js    # Cross-platform error handling
├── SecureStorage.js            # Secure storage with Keychain/AsyncStorage
├── SecureApiClient.js          # Secure API client with interceptors
├── LoginScreen.js              # Login screen with biometric UI
├── HomeScreen.js               # Protected home screen
├── BiometricSettings.js        # Biometric preferences screen
├── App.js                      # Main app with navigation
├── package.json                # Dependencies
├── README.md                   # This file
├── BIOMETRIC_SETUP.md          # Detailed biometric setup guide
└── ...
```

## Installation

1. **Install dependencies:**
```bash
npm install
# or
yarn install
```

2. **Install additional dependencies:**
```bash
# Core dependencies
npx expo install @react-native-async-storage/async-storage
npx expo install @react-navigation/native @react-navigation/stack
npx expo install react-native-screens react-native-safe-area-context
npx expo install react-native-gesture-handler react-native-reanimated

# Biometric authentication dependencies
npm install react-native-biometrics react-native-keychain
```

3. **Platform-specific setup:**
   - See [BIOMETRIC_SETUP.md](./BIOMETRIC_SETUP.md) for detailed platform configuration
   - iOS: Add Face ID usage description to Info.plist
   - Android: Add biometric permissions to AndroidManifest.xml

## Configuration

1. **Update API Base URL:**
   - Open `AuthService.js` and `SecureApiClient.js`
   - Replace `https://your-api-base-url.com/api` with your actual API URL

2. **Backend API Endpoints Required:**
   - `POST /auth/login` - Login endpoint
   - `POST /auth/refresh` - Token refresh endpoint
   - `POST /auth/logout` - Logout endpoint (optional)
   - `GET /user/profile` - Get user profile (example)

## Usage

### 1. AuthService
The main authentication service handles:
- Login/logout operations
- Token storage and retrieval
- Token refresh logic
- Axios interceptor setup

```javascript
import AuthService from './AuthService';

// Login
const result = await AuthService.login(email, password);

// Check authentication
const isAuth = await AuthService.isAuthenticated();

// Logout
await AuthService.logout();
```

### 2. AuthContext
React Context for managing authentication state:

```javascript
import { useAuth } from './AuthContext';

const MyComponent = () => {
  const { isAuthenticated, user, login, logout, isLoading } = useAuth();
  
  // Use auth state and methods
};
```

### 3. Biometric Authentication
Enhanced authentication with biometric support:

```javascript
import { useAuth } from './AuthContext';

const MyComponent = () => {
  const {
    loginWithBiometric,
    enableBiometric,
    disableBiometric,
    biometric
  } = useAuth();

  // Check biometric availability
  if (biometric.isAvailable && biometric.isEnabled) {
    // Login with biometrics
    const result = await loginWithBiometric();
  }

  // Enable biometric authentication
  await enableBiometric(email, password);

  // Disable biometric authentication
  await disableBiometric();
};
```

### 4. SecureApiClient
Secure API client with automatic token refresh:

```javascript
import SecureApiClient from './SecureApiClient';

// Make authenticated API calls
const result = await SecureApiClient.get('/user/profile');
const postResult = await SecureApiClient.post('/data', { key: 'value' });
```

## How Token Refresh Works

1. **Request Interceptor:**
   - Automatically adds access token to all requests
   - Retrieves token from AsyncStorage

2. **Response Interceptor:**
   - Detects 401 (Unauthorized) responses
   - Automatically attempts token refresh
   - Queues failed requests during refresh
   - Retries original request with new token
   - Handles refresh failures by logging out user

3. **Token Refresh Flow:**
   ```
   API Request → 401 Response → Refresh Token → New Access Token → Retry Request
   ```

## How Biometric Authentication Works

1. **Device Capability Check:**
   - Verify biometric hardware availability
   - Check if biometrics are enrolled on device
   - Determine biometric type (Face ID, Touch ID, Fingerprint)

2. **Secure Credential Storage:**
   - Store login credentials in device Keychain (iOS) or Keystore (Android)
   - Use biometric-protected access controls
   - Automatic migration between storage types

3. **Authentication Flow:**
   ```
   Biometric Prompt → Hardware Verification → Credential Retrieval → Auto Login
   ```

4. **Error Handling & Fallback:**
   - Cross-platform error detection and messaging
   - Automatic fallback to password authentication
   - User-friendly error messages with actionable guidance

## API Response Format

Your backend should return tokens in this format:

```javascript
// Login response
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  }
}

// Refresh response
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..." // optional new refresh token
}
```

## Security Considerations

1. **Token Storage:** Uses AsyncStorage (consider react-native-keychain for production)
2. **HTTPS Only:** Always use HTTPS in production
3. **Token Expiry:** Implement proper token expiry times
4. **Refresh Token Rotation:** Consider rotating refresh tokens

## Customization

1. **Add More Screens:** Add registration, forgot password screens
2. **Enhanced Security:** Use react-native-keychain for token storage
3. **Biometric Auth:** Add fingerprint/face ID authentication
4. **Push Notifications:** Add push notification token management

## Testing

Test the authentication flow:

1. **Login:** Enter credentials and verify token storage
2. **API Calls:** Make authenticated requests
3. **Token Refresh:** Wait for token expiry or manually expire token
4. **Logout:** Verify tokens are cleared

## Troubleshooting

1. **Token Refresh Loop:** Check your refresh endpoint and token format
2. **Storage Issues:** Verify AsyncStorage permissions
3. **Navigation Issues:** Ensure proper navigation setup
4. **API Errors:** Check network connectivity and API endpoints

## Production Checklist

- [ ] Replace AsyncStorage with react-native-keychain
- [ ] Add proper error logging
- [ ] Implement biometric authentication
- [ ] Add network connectivity checks
- [ ] Set up proper SSL pinning
- [ ] Add token encryption
- [ ] Implement proper logout on app backgrounding
