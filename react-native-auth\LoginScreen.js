import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { useAuth } from './AuthContext';

const LoginScreen = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showBiometricPrompt, setShowBiometricPrompt] = useState(false);

  const {
    login,
    loginWithBiometric,
    isLoading,
    error,
    clearError,
    clearBiometricError,
    isAuthenticated,
    biometric
  } = useAuth();

  // Navigate to home if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigation.replace('Home');
    }
  }, [isAuthenticated, navigation]);

  // Clear error when component unmounts or inputs change
  useEffect(() => {
    if (error) {
      clearError();
    }
    if (biometric.error) {
      clearBiometricError();
    }
  }, [email, password]);

  // Show biometric prompt on component mount if available and enabled
  useEffect(() => {
    const showBiometricOnMount = async () => {
      if (biometric.isAvailable && biometric.isEnabled && !isAuthenticated) {
        // Small delay to ensure UI is ready
        setTimeout(() => {
          setShowBiometricPrompt(true);
        }, 500);
      }
    };

    showBiometricOnMount();
  }, [biometric.isAvailable, biometric.isEnabled, isAuthenticated]);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    const result = await login(email.trim(), password);

    if (!result.success) {
      Alert.alert('Login Failed', result.error);
    }
  };

  const handleBiometricLogin = async () => {
    setShowBiometricPrompt(false);

    const result = await loginWithBiometric();

    if (!result.success) {
      if (result.fallbackToPassword) {
        // Show password login form
        Alert.alert(
          'Biometric Authentication Failed',
          result.error + '\n\nPlease use your password to login.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Login Failed', result.error);
      }
    }
  };

  const showBiometricLoginPrompt = () => {
    if (!biometric.isAvailable) {
      Alert.alert('Not Available', 'Biometric authentication is not available on this device');
      return;
    }

    if (!biometric.isEnabled) {
      Alert.alert('Not Enabled', 'Biometric authentication is not enabled. Please enable it in settings.');
      return;
    }

    handleBiometricLogin();
  };

  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Text style={styles.title}>Welcome Back!</Text>
          <Text style={styles.subtitle}>Sign in to your account</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Password</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Enter your password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!isLoading}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                <Text style={styles.eyeText}>
                  {showPassword ? '🙈' : '👁️'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {(error || biometric.error) && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error || biometric.error}</Text>
            </View>
          )}

          {/* Biometric Login Button */}
          {biometric.isAvailable && biometric.isEnabled && (
            <TouchableOpacity
              style={[styles.biometricButton, biometric.isLoading && styles.biometricButtonDisabled]}
              onPress={showBiometricLoginPrompt}
              disabled={isLoading || biometric.isLoading}
            >
              {biometric.isLoading ? (
                <ActivityIndicator color="#4F46E5" size="small" />
              ) : (
                <>
                  <Text style={styles.biometricIcon}>
                    {biometric.biometricType === 'FaceID' ? '👤' : '👆'}
                  </Text>
                  <Text style={styles.biometricButtonText}>
                    Login with {biometric.biometricTypeName}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          )}

          {/* Biometric Prompt Modal */}
          {showBiometricPrompt && biometric.isAvailable && biometric.isEnabled && (
            <View style={styles.biometricPromptOverlay}>
              <View style={styles.biometricPromptContainer}>
                <Text style={styles.biometricPromptIcon}>
                  {biometric.biometricType === 'FaceID' ? '👤' : '👆'}
                </Text>
                <Text style={styles.biometricPromptTitle}>
                  {biometric.biometricTypeName} Login
                </Text>
                <Text style={styles.biometricPromptMessage}>
                  Use your {biometric.biometricTypeName.toLowerCase()} to login quickly and securely
                </Text>

                <View style={styles.biometricPromptButtons}>
                  <TouchableOpacity
                    style={styles.biometricPromptButton}
                    onPress={handleBiometricLogin}
                    disabled={biometric.isLoading}
                  >
                    {biometric.isLoading ? (
                      <ActivityIndicator color="#4F46E5" size="small" />
                    ) : (
                      <Text style={styles.biometricPromptButtonText}>
                        Use {biometric.biometricTypeName}
                      </Text>
                    )}
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.biometricPromptCancelButton}
                    onPress={() => setShowBiometricPrompt(false)}
                    disabled={biometric.isLoading}
                  >
                    <Text style={styles.biometricPromptCancelText}>Use Password</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}

          <TouchableOpacity
            style={[styles.loginButton, (isLoading || biometric.isLoading) && styles.loginButtonDisabled]}
            onPress={handleLogin}
            disabled={isLoading || biometric.isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.loginButtonText}>Login with Password</Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.forgotPasswordButton}
            onPress={() => navigation.navigate('ForgotPassword')}
            disabled={isLoading}
          >
            <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Don't have an account? </Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('Register')}
            disabled={isLoading}
          >
            <Text style={styles.signUpText}>Sign Up</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  form: {
    marginBottom: 30,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
  },
  passwordInput: {
    flex: 1,
    padding: 16,
    fontSize: 16,
  },
  eyeButton: {
    padding: 16,
  },
  eyeText: {
    fontSize: 18,
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorText: {
    color: '#DC2626',
    fontSize: 14,
    textAlign: 'center',
  },
  loginButton: {
    backgroundColor: '#4F46E5',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  loginButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  forgotPasswordButton: {
    alignItems: 'center',
  },
  forgotPasswordText: {
    color: '#4F46E5',
    fontSize: 14,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#6B7280',
  },
  signUpText: {
    fontSize: 14,
    color: '#4F46E5',
    fontWeight: '600',
  },
  // Biometric styles
  biometricButton: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 2,
    borderColor: '#4F46E5',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  biometricButtonDisabled: {
    backgroundColor: '#F9FAFB',
    borderColor: '#9CA3AF',
  },
  biometricButtonText: {
    color: '#4F46E5',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  biometricIcon: {
    fontSize: 20,
  },
  // Biometric prompt overlay
  biometricPromptOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  biometricPromptContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    margin: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  biometricPromptIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  biometricPromptTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  biometricPromptMessage: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  biometricPromptButtons: {
    width: '100%',
    gap: 12,
  },
  biometricPromptButton: {
    backgroundColor: '#4F46E5',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  biometricPromptButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  biometricPromptCancelButton: {
    backgroundColor: 'transparent',
    padding: 16,
    alignItems: 'center',
  },
  biometricPromptCancelText: {
    color: '#6B7280',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default LoginScreen;
