import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  ScrollView,
  Switch,
} from 'react-native';
import { useAuth } from './AuthContext';

const BiometricSettings = ({ navigation }) => {
  const [isEnabling, setIsEnabling] = useState(false);
  const [isDisabling, setIsDisabling] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPasswordInput, setShowPasswordInput] = useState(false);

  const { 
    user,
    biometric,
    enableBiometric,
    disableBiometric,
    refreshBiometricInfo,
  } = useAuth();

  useEffect(() => {
    // Refresh biometric info when component mounts
    refreshBiometricInfo();
  }, []);

  const handleToggleBiometric = async (value) => {
    if (value) {
      // Enable biometric
      if (!biometric.isAvailable) {
        Alert.alert(
          'Not Available',
          'Biometric authentication is not available on this device.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Show password input for security
      Alert.prompt(
        'Enable Biometric Login',
        'Please enter your password to enable biometric authentication:',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Enable',
            onPress: async (inputPassword) => {
              if (!inputPassword || inputPassword.trim() === '') {
                Alert.alert('Error', 'Password is required');
                return;
              }
              await enableBiometricAuth(user?.email || '', inputPassword.trim());
            },
          },
        ],
        'secure-text'
      );
    } else {
      // Disable biometric
      Alert.alert(
        'Disable Biometric Login',
        'Are you sure you want to disable biometric authentication?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Disable',
            style: 'destructive',
            onPress: disableBiometricAuth,
          },
        ]
      );
    }
  };

  const enableBiometricAuth = async (email, password) => {
    setIsEnabling(true);
    
    try {
      const result = await enableBiometric(email, password);
      
      if (result.success) {
        Alert.alert(
          'Success',
          `${biometric.biometricTypeName} authentication has been enabled successfully!`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to enable biometric authentication');
    } finally {
      setIsEnabling(false);
    }
  };

  const disableBiometricAuth = async () => {
    setIsDisabling(true);
    
    try {
      const result = await disableBiometric();
      
      if (result.success) {
        Alert.alert(
          'Success',
          'Biometric authentication has been disabled.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to disable biometric authentication');
    } finally {
      setIsDisabling(false);
    }
  };

  const testBiometric = async () => {
    if (!biometric.isAvailable) {
      Alert.alert('Not Available', 'Biometric authentication is not available');
      return;
    }

    if (!biometric.isEnabled) {
      Alert.alert('Not Enabled', 'Please enable biometric authentication first');
      return;
    }

    try {
      // This would trigger the biometric prompt
      Alert.alert(
        'Test Biometric',
        'This would normally trigger a biometric authentication prompt.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Biometric test failed');
    }
  };

  const getBiometricStatusColor = () => {
    if (!biometric.isAvailable) return '#EF4444'; // Red
    if (biometric.isEnabled) return '#10B981'; // Green
    return '#F59E0B'; // Yellow
  };

  const getBiometricStatusText = () => {
    if (!biometric.isAvailable) return 'Not Available';
    if (biometric.isEnabled) return 'Enabled';
    return 'Available';
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Biometric Authentication</Text>
        <Text style={styles.subtitle}>
          Secure your account with {biometric.biometricTypeName.toLowerCase()}
        </Text>
      </View>

      {/* Biometric Status Card */}
      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Status</Text>
          <View style={[styles.statusBadge, { backgroundColor: getBiometricStatusColor() }]}>
            <Text style={styles.statusText}>{getBiometricStatusText()}</Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Authentication Type:</Text>
          <Text style={styles.infoValue}>{biometric.biometricTypeName}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Device Support:</Text>
          <Text style={[styles.infoValue, { color: biometric.isAvailable ? '#10B981' : '#EF4444' }]}>
            {biometric.isAvailable ? 'Supported' : 'Not Supported'}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Current Status:</Text>
          <Text style={[styles.infoValue, { color: biometric.isEnabled ? '#10B981' : '#6B7280' }]}>
            {biometric.isEnabled ? 'Enabled' : 'Disabled'}
          </Text>
        </View>
      </View>

      {/* Settings Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Settings</Text>

        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingTitle}>Enable Biometric Login</Text>
            <Text style={styles.settingDescription}>
              Use {biometric.biometricTypeName.toLowerCase()} to login quickly and securely
            </Text>
          </View>
          <Switch
            value={biometric.isEnabled}
            onValueChange={handleToggleBiometric}
            disabled={!biometric.isAvailable || isEnabling || isDisabling}
            trackColor={{ false: '#D1D5DB', true: '#4F46E5' }}
            thumbColor={biometric.isEnabled ? '#FFFFFF' : '#FFFFFF'}
          />
        </View>

        {biometric.isEnabled && (
          <TouchableOpacity
            style={styles.testButton}
            onPress={testBiometric}
            disabled={isEnabling || isDisabling}
          >
            <Text style={styles.testButtonText}>Test Biometric Authentication</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Information Card */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Security Information</Text>
        
        <View style={styles.infoItem}>
          <Text style={styles.infoItemTitle}>🔒 Secure Storage</Text>
          <Text style={styles.infoItemText}>
            When biometric authentication is enabled, your login credentials are stored securely in the device's keychain/keystore.
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={styles.infoItemTitle}>🔄 Automatic Fallback</Text>
          <Text style={styles.infoItemText}>
            If biometric authentication fails, you can always use your password as a backup.
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Text style={styles.infoItemTitle}>⚡ Quick Access</Text>
          <Text style={styles.infoItemText}>
            Biometric authentication provides faster and more convenient access to your account.
          </Text>
        </View>
      </View>

      {/* Loading Overlay */}
      {(isEnabling || isDisabling) && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#4F46E5" />
            <Text style={styles.loadingText}>
              {isEnabling ? 'Enabling biometric authentication...' : 'Disabling biometric authentication...'}
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  card: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  infoLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    color: '#1F2937',
    fontWeight: '400',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  testButton: {
    backgroundColor: '#4F46E5',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  testButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  infoItem: {
    marginBottom: 16,
  },
  infoItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  infoItemText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    backgroundColor: '#FFFFFF',
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    margin: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#1F2937',
    textAlign: 'center',
  },
});

export default BiometricSettings;
