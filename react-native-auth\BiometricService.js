import ReactNativeBiometrics, { BiometryTypes } from 'react-native-biometrics';
import * as Keychain from 'react-native-keychain';
import { Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Biometric<PERSON>rror<PERSON><PERSON><PERSON> from './BiometricErrorHandler';

const BIOMETRIC_KEYS = {
  BIOMETRIC_ENABLED: 'biometric_enabled',
  BIOMETRIC_CREDENTIALS: 'biometric_credentials',
  BIOMETRIC_PREFERENCE: 'biometric_preference',
};

class BiometricService {
  constructor() {
    this.rnBiometrics = new ReactNativeBiometrics({
      allowDeviceCredentials: true,
    });
    this.isInitialized = false;
    this.biometricType = null;
    this.isAvailable = false;
  }

  // Initialize biometric service
  async initialize() {
    if (this.isInitialized) return;

    try {
      const { available, biometryType } = await this.rnBiometrics.isSensorAvailable();
      this.isAvailable = available;
      this.biometricType = biometryType;
      this.isInitialized = true;

      console.log('Biometric Service Initialized:', {
        available,
        biometryType,
        platform: Platform.OS,
      });
    } catch (error) {
      console.error('Failed to initialize biometric service:', error);
      this.isAvailable = false;
      this.isInitialized = true;
    }
  }

  // Check if biometrics are available on device
  async isBiometricAvailable() {
    await this.initialize();
    return this.isAvailable;
  }

  // Get biometric type (TouchID, FaceID, Fingerprint, etc.)
  async getBiometricType() {
    await this.initialize();
    return this.biometricType;
  }

  // Get user-friendly biometric type name
  getBiometricTypeName(biometryType = null) {
    const type = biometryType || this.biometricType;
    
    switch (type) {
      case BiometryTypes.TouchID:
        return 'Touch ID';
      case BiometryTypes.FaceID:
        return 'Face ID';
      case BiometryTypes.Biometrics:
        return Platform.OS === 'android' ? 'Fingerprint' : 'Biometrics';
      default:
        return 'Biometric Authentication';
    }
  }

  // Check if user has biometric authentication enabled
  async isBiometricEnabled() {
    try {
      const enabled = await AsyncStorage.getItem(BIOMETRIC_KEYS.BIOMETRIC_ENABLED);
      return enabled === 'true';
    } catch (error) {
      console.error('Error checking biometric enabled status:', error);
      return false;
    }
  }

  // Enable biometric authentication
  async enableBiometric(credentials) {
    try {
      const isAvailable = await this.isBiometricAvailable();
      if (!isAvailable) {
        const errorInfo = BiometricErrorHandler.getErrorMessage(
          'BiometryNotAvailable',
          this.getBiometricTypeName()
        );
        return { success: false, error: errorInfo.message };
      }

      // Test biometric authentication first
      const authResult = await this.authenticateWithBiometric('Enable biometric login');
      if (!authResult.success) {
        return {
          success: false,
          error: authResult.error,
          fallbackToPassword: authResult.fallbackToPassword
        };
      }

      // Store credentials securely in Keychain
      await this.storeCredentialsSecurely(credentials);

      // Mark biometric as enabled
      await AsyncStorage.setItem(BIOMETRIC_KEYS.BIOMETRIC_ENABLED, 'true');

      return { success: true };
    } catch (error) {
      BiometricErrorHandler.logError(error, 'EnableBiometric');
      const errorInfo = BiometricErrorHandler.getErrorMessage(error, this.getBiometricTypeName());
      return { success: false, error: errorInfo.message };
    }
  }

  // Disable biometric authentication
  async disableBiometric() {
    try {
      // Remove stored credentials
      await this.removeStoredCredentials();
      
      // Mark biometric as disabled
      await AsyncStorage.setItem(BIOMETRIC_KEYS.BIOMETRIC_ENABLED, 'false');
      
      return { success: true };
    } catch (error) {
      console.error('Error disabling biometric:', error);
      return { success: false, error: error.message };
    }
  }

  // Authenticate with biometric
  async authenticateWithBiometric(promptMessage = 'Authenticate to continue') {
    try {
      const isAvailable = await this.isBiometricAvailable();
      if (!isAvailable) {
        const errorInfo = BiometricErrorHandler.getErrorMessage(
          'BiometryNotAvailable',
          this.getBiometricTypeName()
        );
        return {
          success: false,
          error: errorInfo.message,
          fallbackToPassword: errorInfo.fallbackToPassword
        };
      }

      const { success, error } = await this.rnBiometrics.simplePrompt({
        promptMessage,
        cancelButtonText: 'Use Password',
      });

      if (success) {
        return { success: true };
      } else {
        BiometricErrorHandler.logError(error, 'BiometricAuth');
        const errorInfo = BiometricErrorHandler.getErrorMessage(error, this.getBiometricTypeName());

        return {
          success: false,
          error: errorInfo.message,
          fallbackToPassword: errorInfo.fallbackToPassword,
          canRetry: errorInfo.canRetry,
          retryDelay: errorInfo.retryDelay
        };
      }
    } catch (error) {
      BiometricErrorHandler.logError(error, 'BiometricAuth');
      const errorInfo = BiometricErrorHandler.getErrorMessage(error, this.getBiometricTypeName());

      return {
        success: false,
        error: errorInfo.message,
        fallbackToPassword: errorInfo.fallbackToPassword
      };
    }
  }

  // Store credentials securely in Keychain
  async storeCredentialsSecurely(credentials) {
    try {
      const keychainOptions = {
        service: 'com.yourapp.biometric',
        accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY,
        authenticatePrompt: 'Authenticate to access your account',
        showModal: true,
        kLocalizedFallbackTitle: 'Use Password',
      };

      await Keychain.setInternetCredentials(
        BIOMETRIC_KEYS.BIOMETRIC_CREDENTIALS,
        credentials.email || credentials.username,
        JSON.stringify(credentials),
        keychainOptions
      );
    } catch (error) {
      console.error('Error storing credentials in keychain:', error);
      throw new Error('Failed to store credentials securely');
    }
  }

  // Retrieve credentials from Keychain
  async getStoredCredentials() {
    try {
      const keychainOptions = {
        service: 'com.yourapp.biometric',
        authenticatePrompt: 'Authenticate to access your account',
        showModal: true,
        kLocalizedFallbackTitle: 'Use Password',
      };

      const credentials = await Keychain.getInternetCredentials(
        BIOMETRIC_KEYS.BIOMETRIC_CREDENTIALS,
        keychainOptions
      );

      if (credentials && credentials.password) {
        return {
          success: true,
          credentials: JSON.parse(credentials.password),
          username: credentials.username,
        };
      } else {
        return { success: false, error: 'No stored credentials found' };
      }
    } catch (error) {
      console.error('Error retrieving credentials from keychain:', error);
      return { 
        success: false, 
        error: 'Failed to retrieve stored credentials',
        fallbackToPassword: true 
      };
    }
  }

  // Remove stored credentials
  async removeStoredCredentials() {
    try {
      await Keychain.resetInternetCredentials(BIOMETRIC_KEYS.BIOMETRIC_CREDENTIALS);
    } catch (error) {
      console.error('Error removing stored credentials:', error);
    }
  }

  // Migrate tokens from AsyncStorage to Keychain when biometrics are enabled
  async migrateToSecureStorage(tokens) {
    try {
      const keychainOptions = {
        service: 'com.yourapp.tokens',
        accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY_OR_DEVICE_PASSCODE,
        authenticatePrompt: 'Authenticate to access your tokens',
      };

      await Keychain.setInternetCredentials(
        'app_tokens',
        'tokens',
        JSON.stringify(tokens),
        keychainOptions
      );

      // Remove from AsyncStorage
      await AsyncStorage.multiRemove(['accessToken', 'refreshToken']);
    } catch (error) {
      console.error('Error migrating to secure storage:', error);
      throw error;
    }
  }

  // Get tokens from secure storage
  async getTokensFromSecureStorage() {
    try {
      const result = await Keychain.getInternetCredentials('app_tokens');
      if (result && result.password) {
        return JSON.parse(result.password);
      }
      return null;
    } catch (error) {
      console.error('Error getting tokens from secure storage:', error);
      return null;
    }
  }



  // Check if biometric enrollment has changed
  async checkBiometricEnrollmentChange() {
    try {
      const { available, biometryType } = await this.rnBiometrics.isSensorAvailable();
      
      if (!available && await this.isBiometricEnabled()) {
        // Biometrics were enabled but now unavailable
        await this.disableBiometric();
        return { changed: true, disabled: true };
      }
      
      return { changed: false };
    } catch (error) {
      console.error('Error checking biometric enrollment:', error);
      return { changed: false };
    }
  }
}

export default new BiometricService();
