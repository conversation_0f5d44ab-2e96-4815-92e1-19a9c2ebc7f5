import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Keychain from 'react-native-keychain';
import BiometricService from './BiometricService';

const STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_DATA: 'userData',
  BIOMETRIC_ENABLED: 'biometric_enabled',
};

const KEYCHAIN_SERVICES = {
  TOKENS: 'com.yourapp.tokens',
  USER_DATA: 'com.yourapp.userdata',
};

class SecureStorage {
  constructor() {
    this.isBiometricStorageEnabled = false;
    this.initializeStorageMode();
  }

  // Initialize storage mode based on biometric settings
  async initializeStorageMode() {
    try {
      const biometricEnabled = await BiometricService.isBiometricEnabled();
      const biometricAvailable = await BiometricService.isBiometricAvailable();
      
      this.isBiometricStorageEnabled = biometricEnabled && biometricAvailable;
      
      // If biometrics were enabled but now unavailable, migrate back to AsyncStorage
      if (biometricEnabled && !biometricAvailable) {
        await this.migrateFromSecureStorage();
        await BiometricService.disableBiometric();
      }
    } catch (error) {
      console.error('Error initializing storage mode:', error);
      this.isBiometricStorageEnabled = false;
    }
  }

  // Store tokens with appropriate storage method
  async storeTokens(accessToken, refreshToken) {
    try {
      if (this.isBiometricStorageEnabled) {
        await this.storeTokensInKeychain(accessToken, refreshToken);
      } else {
        await this.storeTokensInAsyncStorage(accessToken, refreshToken);
      }
    } catch (error) {
      console.error('Error storing tokens:', error);
      // Fallback to AsyncStorage if Keychain fails
      await this.storeTokensInAsyncStorage(accessToken, refreshToken);
    }
  }

  // Get tokens from appropriate storage
  async getTokens() {
    try {
      if (this.isBiometricStorageEnabled) {
        return await this.getTokensFromKeychain();
      } else {
        return await this.getTokensFromAsyncStorage();
      }
    } catch (error) {
      console.error('Error getting tokens:', error);
      // Fallback to AsyncStorage
      return await this.getTokensFromAsyncStorage();
    }
  }

  // Store user data with appropriate storage method
  async storeUserData(userData) {
    try {
      if (this.isBiometricStorageEnabled) {
        await this.storeUserDataInKeychain(userData);
      } else {
        await this.storeUserDataInAsyncStorage(userData);
      }
    } catch (error) {
      console.error('Error storing user data:', error);
      // Fallback to AsyncStorage
      await this.storeUserDataInAsyncStorage(userData);
    }
  }

  // Get user data from appropriate storage
  async getUserData() {
    try {
      if (this.isBiometricStorageEnabled) {
        return await this.getUserDataFromKeychain();
      } else {
        return await this.getUserDataFromAsyncStorage();
      }
    } catch (error) {
      console.error('Error getting user data:', error);
      // Fallback to AsyncStorage
      return await this.getUserDataFromAsyncStorage();
    }
  }

  // Clear all stored data
  async clearAllData() {
    try {
      // Clear from both storage methods to ensure complete cleanup
      await this.clearAsyncStorageData();
      await this.clearKeychainData();
    } catch (error) {
      console.error('Error clearing all data:', error);
    }
  }

  // Migrate data to secure storage (Keychain) when biometrics are enabled
  async migrateToSecureStorage() {
    try {
      // Get existing data from AsyncStorage
      const tokens = await this.getTokensFromAsyncStorage();
      const userData = await this.getUserDataFromAsyncStorage();

      if (tokens.accessToken || tokens.refreshToken) {
        // Store in Keychain
        await this.storeTokensInKeychain(tokens.accessToken, tokens.refreshToken);
        
        if (userData) {
          await this.storeUserDataInKeychain(userData);
        }

        // Clear from AsyncStorage
        await AsyncStorage.multiRemove([
          STORAGE_KEYS.ACCESS_TOKEN,
          STORAGE_KEYS.REFRESH_TOKEN,
          STORAGE_KEYS.USER_DATA,
        ]);

        this.isBiometricStorageEnabled = true;
        console.log('Successfully migrated data to secure storage');
      }
    } catch (error) {
      console.error('Error migrating to secure storage:', error);
      throw error;
    }
  }

  // Migrate data from secure storage back to AsyncStorage
  async migrateFromSecureStorage() {
    try {
      // Get data from Keychain (without biometric prompt for migration)
      const tokens = await this.getTokensFromKeychainSilent();
      const userData = await this.getUserDataFromKeychainSilent();

      if (tokens && (tokens.accessToken || tokens.refreshToken)) {
        // Store in AsyncStorage
        await this.storeTokensInAsyncStorage(tokens.accessToken, tokens.refreshToken);
        
        if (userData) {
          await this.storeUserDataInAsyncStorage(userData);
        }
      }

      // Clear from Keychain
      await this.clearKeychainData();
      
      this.isBiometricStorageEnabled = false;
      console.log('Successfully migrated data from secure storage');
    } catch (error) {
      console.error('Error migrating from secure storage:', error);
      // Don't throw error for migration failures
    }
  }

  // AsyncStorage methods
  async storeTokensInAsyncStorage(accessToken, refreshToken) {
    const items = [];
    if (accessToken) items.push([STORAGE_KEYS.ACCESS_TOKEN, accessToken]);
    if (refreshToken) items.push([STORAGE_KEYS.REFRESH_TOKEN, refreshToken]);
    
    if (items.length > 0) {
      await AsyncStorage.multiSet(items);
    }
  }

  async getTokensFromAsyncStorage() {
    try {
      const [accessToken, refreshToken] = await AsyncStorage.multiGet([
        STORAGE_KEYS.ACCESS_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
      ]);

      return {
        accessToken: accessToken[1],
        refreshToken: refreshToken[1],
      };
    } catch (error) {
      console.error('Error getting tokens from AsyncStorage:', error);
      return { accessToken: null, refreshToken: null };
    }
  }

  async storeUserDataInAsyncStorage(userData) {
    await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
  }

  async getUserDataFromAsyncStorage() {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting user data from AsyncStorage:', error);
      return null;
    }
  }

  async clearAsyncStorageData() {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.ACCESS_TOKEN,
      STORAGE_KEYS.REFRESH_TOKEN,
      STORAGE_KEYS.USER_DATA,
    ]);
  }

  // Keychain methods
  async storeTokensInKeychain(accessToken, refreshToken) {
    const keychainOptions = {
      service: KEYCHAIN_SERVICES.TOKENS,
      accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY_OR_DEVICE_PASSCODE,
      authenticatePrompt: 'Authenticate to store your tokens securely',
    };

    const tokenData = {
      accessToken: accessToken || null,
      refreshToken: refreshToken || null,
      timestamp: Date.now(),
    };

    await Keychain.setInternetCredentials(
      'app_tokens',
      'tokens',
      JSON.stringify(tokenData),
      keychainOptions
    );
  }

  async getTokensFromKeychain() {
    try {
      const keychainOptions = {
        service: KEYCHAIN_SERVICES.TOKENS,
        authenticatePrompt: 'Authenticate to access your tokens',
      };

      const result = await Keychain.getInternetCredentials('app_tokens', keychainOptions);
      
      if (result && result.password) {
        const tokenData = JSON.parse(result.password);
        return {
          accessToken: tokenData.accessToken,
          refreshToken: tokenData.refreshToken,
        };
      }
      
      return { accessToken: null, refreshToken: null };
    } catch (error) {
      console.error('Error getting tokens from Keychain:', error);
      return { accessToken: null, refreshToken: null };
    }
  }

  // Silent keychain access for migration (no biometric prompt)
  async getTokensFromKeychainSilent() {
    try {
      const result = await Keychain.getInternetCredentials('app_tokens');
      
      if (result && result.password) {
        const tokenData = JSON.parse(result.password);
        return {
          accessToken: tokenData.accessToken,
          refreshToken: tokenData.refreshToken,
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error getting tokens from Keychain silently:', error);
      return null;
    }
  }

  async storeUserDataInKeychain(userData) {
    const keychainOptions = {
      service: KEYCHAIN_SERVICES.USER_DATA,
      accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY_OR_DEVICE_PASSCODE,
      authenticatePrompt: 'Authenticate to store your profile securely',
    };

    await Keychain.setInternetCredentials(
      'user_data',
      'userdata',
      JSON.stringify(userData),
      keychainOptions
    );
  }

  async getUserDataFromKeychain() {
    try {
      const result = await Keychain.getInternetCredentials('user_data');
      
      if (result && result.password) {
        return JSON.parse(result.password);
      }
      
      return null;
    } catch (error) {
      console.error('Error getting user data from Keychain:', error);
      return null;
    }
  }

  async getUserDataFromKeychainSilent() {
    try {
      const result = await Keychain.getInternetCredentials('user_data');
      return result && result.password ? JSON.parse(result.password) : null;
    } catch (error) {
      return null;
    }
  }

  async clearKeychainData() {
    try {
      await Keychain.resetInternetCredentials('app_tokens');
      await Keychain.resetInternetCredentials('user_data');
    } catch (error) {
      console.error('Error clearing Keychain data:', error);
    }
  }

  // Utility methods
  async isUsingSecureStorage() {
    return this.isBiometricStorageEnabled;
  }

  async enableSecureStorage() {
    await this.migrateToSecureStorage();
    this.isBiometricStorageEnabled = true;
  }

  async disableSecureStorage() {
    await this.migrateFromSecureStorage();
    this.isBiometricStorageEnabled = false;
  }
}

export default new SecureStorage();
