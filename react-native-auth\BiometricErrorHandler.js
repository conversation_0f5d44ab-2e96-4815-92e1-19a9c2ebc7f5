import { Platform, Alert } from 'react-native';

// Biometric error codes for different platforms
const IOS_ERROR_CODES = {
  BiometryNotAvailable: 'BiometryNotAvailable',
  BiometryNotEnrolled: 'BiometryNotEnrolled',
  BiometryLockout: 'BiometryLockout',
  BiometryLockoutPermanent: 'BiometryLockoutPermanent',
  UserCancel: 'UserCancel',
  UserFallback: 'UserFallback',
  SystemCancel: 'SystemCancel',
  PasscodeNotSet: 'PasscodeNotSet',
  TouchIDNotAvailable: 'TouchIDNotAvailable',
  TouchIDNotEnrolled: 'TouchIDNotEnrolled',
  TouchIDLockout: 'TouchIDLockout',
  FaceIDNotAvailable: 'FaceIDNotAvailable',
  FaceIDNotEnrolled: 'FaceIDNotEnrolled',
  FaceIDLockout: 'FaceIDLockout',
};

const ANDROID_ERROR_CODES = {
  BiometricErrorHwUnavailable: 'BiometricErrorHwUnavailable',
  BiometricErrorNoneEnrolled: 'BiometricErrorNoneEnrolled',
  BiometricErrorNoSpace: 'BiometricErrorNoSpace',
  BiometricErrorTimeout: 'BiometricErrorTimeout',
  BiometricErrorCanceled: 'BiometricErrorCanceled',
  BiometricErrorLockout: 'BiometricErrorLockout',
  BiometricErrorLockoutPermanent: 'BiometricErrorLockoutPermanent',
  BiometricErrorUserCancel: 'BiometricErrorUserCancel',
  BiometricErrorNoDeviceCredential: 'BiometricErrorNoDeviceCredential',
  BiometricErrorSecurityUpdateRequired: 'BiometricErrorSecurityUpdateRequired',
  UserCancel: 'UserCancel',
  FingerprintScannerNotAvailable: 'FingerprintScannerNotAvailable',
  FingerprintScannerNotEnrolled: 'FingerprintScannerNotEnrolled',
};

class BiometricErrorHandler {
  // Get user-friendly error message based on platform and error code
  static getErrorMessage(error, biometricType = 'Biometric Authentication') {
    const errorCode = error?.code || error?.message || error;
    
    if (Platform.OS === 'ios') {
      return this.getIOSErrorMessage(errorCode, biometricType);
    } else {
      return this.getAndroidErrorMessage(errorCode, biometricType);
    }
  }

  // iOS specific error messages
  static getIOSErrorMessage(errorCode, biometricType) {
    switch (errorCode) {
      case IOS_ERROR_CODES.BiometryNotAvailable:
      case IOS_ERROR_CODES.TouchIDNotAvailable:
      case IOS_ERROR_CODES.FaceIDNotAvailable:
        return {
          title: 'Not Available',
          message: `${biometricType} is not available on this device. Please check your device settings.`,
          fallbackToPassword: true,
          canRetry: false,
        };

      case IOS_ERROR_CODES.BiometryNotEnrolled:
      case IOS_ERROR_CODES.TouchIDNotEnrolled:
      case IOS_ERROR_CODES.FaceIDNotEnrolled:
        return {
          title: 'Not Set Up',
          message: `${biometricType} is not set up on this device. Please add your biometric data in Settings.`,
          fallbackToPassword: true,
          canRetry: false,
          actionText: 'Go to Settings',
        };

      case IOS_ERROR_CODES.BiometryLockout:
      case IOS_ERROR_CODES.TouchIDLockout:
      case IOS_ERROR_CODES.FaceIDLockout:
        return {
          title: 'Temporarily Locked',
          message: `${biometricType} is temporarily locked due to too many failed attempts. Please try again later or use your passcode.`,
          fallbackToPassword: true,
          canRetry: true,
          retryDelay: 30000, // 30 seconds
        };

      case IOS_ERROR_CODES.BiometryLockoutPermanent:
        return {
          title: 'Permanently Locked',
          message: `${biometricType} is locked. Please unlock your device with your passcode to re-enable biometric authentication.`,
          fallbackToPassword: true,
          canRetry: false,
        };

      case IOS_ERROR_CODES.UserCancel:
        return {
          title: 'Cancelled',
          message: 'Authentication was cancelled by the user.',
          fallbackToPassword: true,
          canRetry: true,
        };

      case IOS_ERROR_CODES.UserFallback:
        return {
          title: 'Use Password',
          message: 'User chose to use password instead of biometric authentication.',
          fallbackToPassword: true,
          canRetry: false,
        };

      case IOS_ERROR_CODES.SystemCancel:
        return {
          title: 'System Cancelled',
          message: 'Authentication was cancelled by the system (e.g., another app came to foreground).',
          fallbackToPassword: true,
          canRetry: true,
        };

      case IOS_ERROR_CODES.PasscodeNotSet:
        return {
          title: 'Passcode Required',
          message: 'Device passcode is not set. Please set up a passcode in Settings to use biometric authentication.',
          fallbackToPassword: false,
          canRetry: false,
          actionText: 'Go to Settings',
        };

      default:
        return {
          title: 'Authentication Failed',
          message: `${biometricType} authentication failed. Please try again or use your password.`,
          fallbackToPassword: true,
          canRetry: true,
        };
    }
  }

  // Android specific error messages
  static getAndroidErrorMessage(errorCode, biometricType) {
    switch (errorCode) {
      case ANDROID_ERROR_CODES.BiometricErrorHwUnavailable:
      case ANDROID_ERROR_CODES.FingerprintScannerNotAvailable:
        return {
          title: 'Hardware Unavailable',
          message: 'Biometric hardware is currently unavailable. Please try again later or use your password.',
          fallbackToPassword: true,
          canRetry: true,
        };

      case ANDROID_ERROR_CODES.BiometricErrorNoneEnrolled:
      case ANDROID_ERROR_CODES.FingerprintScannerNotEnrolled:
        return {
          title: 'Not Set Up',
          message: `No ${biometricType.toLowerCase()} is enrolled on this device. Please add your biometric data in Settings.`,
          fallbackToPassword: true,
          canRetry: false,
          actionText: 'Go to Settings',
        };

      case ANDROID_ERROR_CODES.BiometricErrorNoSpace:
        return {
          title: 'Storage Full',
          message: 'Not enough storage space available for biometric authentication.',
          fallbackToPassword: true,
          canRetry: false,
        };

      case ANDROID_ERROR_CODES.BiometricErrorTimeout:
        return {
          title: 'Timeout',
          message: 'Biometric authentication timed out. Please try again.',
          fallbackToPassword: true,
          canRetry: true,
        };

      case ANDROID_ERROR_CODES.BiometricErrorCanceled:
      case ANDROID_ERROR_CODES.BiometricErrorUserCancel:
      case ANDROID_ERROR_CODES.UserCancel:
        return {
          title: 'Cancelled',
          message: 'Authentication was cancelled.',
          fallbackToPassword: true,
          canRetry: true,
        };

      case ANDROID_ERROR_CODES.BiometricErrorLockout:
        return {
          title: 'Temporarily Locked',
          message: 'Too many failed attempts. Biometric authentication is temporarily disabled. Please try again in 30 seconds.',
          fallbackToPassword: true,
          canRetry: true,
          retryDelay: 30000,
        };

      case ANDROID_ERROR_CODES.BiometricErrorLockoutPermanent:
        return {
          title: 'Locked Out',
          message: 'Biometric authentication is disabled due to too many failed attempts. Please unlock your device to re-enable.',
          fallbackToPassword: true,
          canRetry: false,
        };

      case ANDROID_ERROR_CODES.BiometricErrorNoDeviceCredential:
        return {
          title: 'No Device Credential',
          message: 'No device credential (PIN, pattern, or password) is set up. Please set up device security in Settings.',
          fallbackToPassword: false,
          canRetry: false,
          actionText: 'Go to Settings',
        };

      case ANDROID_ERROR_CODES.BiometricErrorSecurityUpdateRequired:
        return {
          title: 'Security Update Required',
          message: 'A security update is required to use biometric authentication.',
          fallbackToPassword: true,
          canRetry: false,
        };

      default:
        return {
          title: 'Authentication Failed',
          message: `${biometricType} authentication failed. Please try again or use your password.`,
          fallbackToPassword: true,
          canRetry: true,
        };
    }
  }

  // Show error alert with appropriate actions
  static showErrorAlert(error, biometricType, onRetry, onFallback, onSettings) {
    const errorInfo = this.getErrorMessage(error, biometricType);
    const buttons = [];

    // Add retry button if applicable
    if (errorInfo.canRetry && onRetry) {
      buttons.push({
        text: 'Try Again',
        onPress: () => {
          if (errorInfo.retryDelay) {
            setTimeout(onRetry, errorInfo.retryDelay);
          } else {
            onRetry();
          }
        },
      });
    }

    // Add settings button if applicable
    if (errorInfo.actionText && onSettings) {
      buttons.push({
        text: errorInfo.actionText,
        onPress: onSettings,
      });
    }

    // Add fallback button if applicable
    if (errorInfo.fallbackToPassword && onFallback) {
      buttons.push({
        text: 'Use Password',
        onPress: onFallback,
      });
    }

    // Always add a dismiss button
    buttons.push({
      text: 'OK',
      style: 'cancel',
    });

    Alert.alert(errorInfo.title, errorInfo.message, buttons);

    return errorInfo;
  }

  // Check if error should trigger fallback to password
  static shouldFallbackToPassword(error) {
    const errorInfo = this.getErrorMessage(error);
    return errorInfo.fallbackToPassword;
  }

  // Check if error allows retry
  static canRetry(error) {
    const errorInfo = this.getErrorMessage(error);
    return errorInfo.canRetry;
  }

  // Get retry delay if applicable
  static getRetryDelay(error) {
    const errorInfo = this.getErrorMessage(error);
    return errorInfo.retryDelay || 0;
  }

  // Handle common biometric setup errors
  static handleSetupError(error, biometricType) {
    const errorInfo = this.getErrorMessage(error, biometricType);
    
    if (errorInfo.actionText === 'Go to Settings') {
      Alert.alert(
        errorInfo.title,
        errorInfo.message + '\n\nWould you like to open Settings now?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Open Settings',
            onPress: () => {
              // In a real app, you would use Linking.openSettings() or similar
              console.log('Opening device settings...');
            },
          },
        ]
      );
    } else {
      Alert.alert(errorInfo.title, errorInfo.message);
    }

    return errorInfo;
  }

  // Log error for debugging
  static logError(error, context = 'BiometricAuth') {
    console.error(`[${context}] Biometric Error:`, {
      platform: Platform.OS,
      error: error,
      code: error?.code,
      message: error?.message,
      timestamp: new Date().toISOString(),
    });
  }
}

export default BiometricErrorHandler;
