import axios from 'axios';
import BiometricService from './BiometricService';
import SecureStorage from './SecureStorage';

const API_BASE_URL = 'https://your-api-base-url.com/api';

class AuthService {
  constructor() {
    this.setupAxiosInterceptors();
    this.initializeBiometricService();
  }

  // Initialize biometric service
  async initializeBiometricService() {
    try {
      await BiometricService.initialize();
      await SecureStorage.initializeStorageMode();
    } catch (error) {
      console.error('Failed to initialize biometric service:', error);
    }
  }

  // Setup axios interceptors for automatic token refresh
  setupAxiosInterceptors() {
    // Request interceptor to add token to headers
    axios.interceptors.request.use(
      async (config) => {
        const token = await this.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token refresh
    axios.interceptors.response.use(
      (response) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const newToken = await this.refreshAccessToken();
            if (newToken) {
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              return axios(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            await this.logout();
            // You can emit an event here to redirect to login screen
            // EventEmitter.emit('LOGOUT_REQUIRED');
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Login method
  async login(email, password, isBiometricLogin = false) {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email,
        password,
      });

      const { accessToken, refreshToken, user } = response.data;

      // Store tokens securely
      await this.storeTokens(accessToken, refreshToken);
      await this.storeUserData(user);

      return {
        success: true,
        user,
        accessToken,
        isBiometricLogin,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Login failed',
        isBiometricLogin,
      };
    }
  }

  // Refresh access token using refresh token
  async refreshAccessToken() {
    try {
      const refreshToken = await this.getRefreshToken();
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
        refreshToken,
      });

      const { accessToken, refreshToken: newRefreshToken } = response.data;

      // Store new tokens
      await this.storeTokens(accessToken, newRefreshToken || refreshToken);

      return accessToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  }

  // Store tokens securely
  async storeTokens(accessToken, refreshToken) {
    try {
      await SecureStorage.storeTokens(accessToken, refreshToken);
    } catch (error) {
      console.error('Error storing tokens:', error);
    }
  }

  // Get access token
  async getAccessToken() {
    try {
      const tokens = await SecureStorage.getTokens();
      return tokens.accessToken;
    } catch (error) {
      console.error('Error getting access token:', error);
      return null;
    }
  }

  // Get refresh token
  async getRefreshToken() {
    try {
      const tokens = await SecureStorage.getTokens();
      return tokens.refreshToken;
    } catch (error) {
      console.error('Error getting refresh token:', error);
      return null;
    }
  }

  // Store user data
  async storeUserData(user) {
    try {
      await SecureStorage.storeUserData(user);
    } catch (error) {
      console.error('Error storing user data:', error);
    }
  }

  // Get user data
  async getUserData() {
    try {
      return await SecureStorage.getUserData();
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }

  // Check if user is authenticated
  async isAuthenticated() {
    const accessToken = await this.getAccessToken();
    const refreshToken = await this.getRefreshToken();
    return !!(accessToken && refreshToken);
  }

  // Biometric login
  async loginWithBiometric() {
    try {
      // Check if biometric is available and enabled
      const isBiometricEnabled = await BiometricService.isBiometricEnabled();
      const isBiometricAvailable = await BiometricService.isBiometricAvailable();

      if (!isBiometricEnabled || !isBiometricAvailable) {
        return {
          success: false,
          error: 'Biometric authentication is not available',
          fallbackToPassword: true,
        };
      }

      // Get stored credentials
      const credentialsResult = await BiometricService.getStoredCredentials();

      if (!credentialsResult.success) {
        return {
          success: false,
          error: credentialsResult.error,
          fallbackToPassword: credentialsResult.fallbackToPassword,
        };
      }

      const { credentials } = credentialsResult;

      // Perform login with stored credentials
      const loginResult = await this.login(credentials.email, credentials.password, true);

      return loginResult;
    } catch (error) {
      console.error('Biometric login error:', error);
      return {
        success: false,
        error: 'Biometric login failed',
        fallbackToPassword: true,
      };
    }
  }

  // Enable biometric authentication
  async enableBiometric(email, password) {
    try {
      const credentials = { email, password };
      const result = await BiometricService.enableBiometric(credentials);

      if (result.success) {
        // Migrate to secure storage
        await SecureStorage.enableSecureStorage();
      }

      return result;
    } catch (error) {
      console.error('Error enabling biometric:', error);
      return { success: false, error: error.message };
    }
  }

  // Disable biometric authentication
  async disableBiometric() {
    try {
      const result = await BiometricService.disableBiometric();

      if (result.success) {
        // Migrate back to AsyncStorage
        await SecureStorage.disableSecureStorage();
      }

      return result;
    } catch (error) {
      console.error('Error disabling biometric:', error);
      return { success: false, error: error.message };
    }
  }

  // Check biometric availability
  async getBiometricInfo() {
    try {
      const isAvailable = await BiometricService.isBiometricAvailable();
      const isEnabled = await BiometricService.isBiometricEnabled();
      const biometricType = await BiometricService.getBiometricType();
      const biometricTypeName = BiometricService.getBiometricTypeName(biometricType);

      return {
        isAvailable,
        isEnabled,
        biometricType,
        biometricTypeName,
      };
    } catch (error) {
      console.error('Error getting biometric info:', error);
      return {
        isAvailable: false,
        isEnabled: false,
        biometricType: null,
        biometricTypeName: 'Biometric Authentication',
      };
    }
  }

  // Logout
  async logout() {
    try {
      // Optional: Call logout endpoint to invalidate tokens on server
      const refreshToken = await this.getRefreshToken();
      if (refreshToken) {
        await axios.post(`${API_BASE_URL}/auth/logout`, {
          refreshToken,
        });
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      // Clear all stored data
      await SecureStorage.clearAllData();
    }
  }

  // Make authenticated API calls
  async makeAuthenticatedRequest(method, endpoint, data = null) {
    try {
      const config = {
        method,
        url: `${API_BASE_URL}${endpoint}`,
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Request failed',
      };
    }
  }
}

export default new AuthService();
