# 🚀 Biometric Authentication Usage Examples

Complete code examples showing how to use the biometric authentication system.

## 📱 Basic Usage in Components

### Login Screen with Biometric Support

```javascript
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { useAuth } from './AuthContext';

const LoginScreen = () => {
  const { 
    login, 
    loginWithBiometric, 
    biometric, 
    isLoading 
  } = useAuth();

  // Auto-show biometric prompt if available and enabled
  useEffect(() => {
    if (biometric.isAvailable && biometric.isEnabled) {
      setTimeout(() => handleBiometricLogin(), 500);
    }
  }, [biometric.isAvailable, biometric.isEnabled]);

  const handleBiometricLogin = async () => {
    const result = await loginWithBiometric();
    
    if (!result.success && result.fallbackToPassword) {
      Alert.alert(
        'Biometric Failed', 
        result.error + '\n\nPlease use your password.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <View>
      {/* Biometric Login Button */}
      {biometric.isAvailable && biometric.isEnabled && (
        <TouchableOpacity onPress={handleBiometricLogin}>
          <Text>Login with {biometric.biometricTypeName}</Text>
        </TouchableOpacity>
      )}
      
      {/* Regular login form */}
      {/* ... email/password inputs ... */}
    </View>
  );
};
```

### Settings Screen for Biometric Management

```javascript
import React from 'react';
import { View, Text, Switch, Alert } from 'react-native';
import { useAuth } from './AuthContext';

const SettingsScreen = () => {
  const { 
    biometric, 
    enableBiometric, 
    disableBiometric, 
    user 
  } = useAuth();

  const handleToggleBiometric = async (enabled) => {
    if (enabled) {
      // Enable biometric - requires password confirmation
      Alert.prompt(
        'Enable Biometric Login',
        'Enter your password to enable biometric authentication:',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Enable',
            onPress: async (password) => {
              const result = await enableBiometric(user.email, password);
              if (result.success) {
                Alert.alert('Success', 'Biometric authentication enabled!');
              } else {
                Alert.alert('Error', result.error);
              }
            }
          }
        ],
        'secure-text'
      );
    } else {
      // Disable biometric
      const result = await disableBiometric();
      if (result.success) {
        Alert.alert('Success', 'Biometric authentication disabled.');
      }
    }
  };

  return (
    <View>
      <Text>Biometric Authentication</Text>
      <Text>Type: {biometric.biometricTypeName}</Text>
      <Text>Available: {biometric.isAvailable ? 'Yes' : 'No'}</Text>
      
      <Switch
        value={biometric.isEnabled}
        onValueChange={handleToggleBiometric}
        disabled={!biometric.isAvailable}
      />
    </View>
  );
};
```

## 🔧 Advanced Usage Patterns

### Custom Biometric Prompt Component

```javascript
import React, { useState } from 'react';
import { Modal, View, Text, TouchableOpacity } from 'react-native';
import { useAuth } from './AuthContext';

const BiometricPrompt = ({ visible, onClose, onSuccess }) => {
  const { loginWithBiometric, biometric } = useAuth();
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const handleAuthenticate = async () => {
    setIsAuthenticating(true);
    
    const result = await loginWithBiometric();
    
    if (result.success) {
      onSuccess();
    } else if (result.fallbackToPassword) {
      onClose();
      // Show password login
    }
    
    setIsAuthenticating(false);
  };

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.icon}>
            {biometric.biometricType === 'FaceID' ? '👤' : '👆'}
          </Text>
          <Text style={styles.title}>
            {biometric.biometricTypeName} Login
          </Text>
          <Text style={styles.message}>
            Use your {biometric.biometricTypeName.toLowerCase()} to login securely
          </Text>
          
          <TouchableOpacity 
            onPress={handleAuthenticate}
            disabled={isAuthenticating}
          >
            <Text>Authenticate</Text>
          </TouchableOpacity>
          
          <TouchableOpacity onPress={onClose}>
            <Text>Use Password</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};
```

### Biometric Status Hook

```javascript
import { useState, useEffect } from 'react';
import { useAuth } from './AuthContext';

export const useBiometricStatus = () => {
  const { biometric, refreshBiometricInfo } = useAuth();
  const [status, setStatus] = useState('checking');

  useEffect(() => {
    checkBiometricStatus();
  }, []);

  const checkBiometricStatus = async () => {
    await refreshBiometricInfo();
    
    if (!biometric.isAvailable) {
      setStatus('unavailable');
    } else if (!biometric.isEnabled) {
      setStatus('available');
    } else {
      setStatus('enabled');
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'unavailable':
        return `${biometric.biometricTypeName} is not available on this device`;
      case 'available':
        return `${biometric.biometricTypeName} is available but not enabled`;
      case 'enabled':
        return `${biometric.biometricTypeName} is enabled and ready`;
      default:
        return 'Checking biometric status...';
    }
  };

  return {
    status,
    message: getStatusMessage(),
    isAvailable: biometric.isAvailable,
    isEnabled: biometric.isEnabled,
    biometricType: biometric.biometricTypeName,
    refresh: checkBiometricStatus,
  };
};
```

## 🛡️ Error Handling Examples

### Comprehensive Error Handling

```javascript
import { useAuth } from './AuthContext';
import BiometricErrorHandler from './BiometricErrorHandler';

const BiometricLoginComponent = () => {
  const { loginWithBiometric, biometric } = useAuth();

  const handleBiometricLogin = async () => {
    try {
      const result = await loginWithBiometric();
      
      if (!result.success) {
        // Use the error handler for user-friendly messages
        BiometricErrorHandler.showErrorAlert(
          result.error,
          biometric.biometricTypeName,
          // Retry callback
          () => handleBiometricLogin(),
          // Fallback to password callback
          () => showPasswordLogin(),
          // Settings callback
          () => openDeviceSettings()
        );
      }
    } catch (error) {
      console.error('Biometric login error:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    }
  };

  const showPasswordLogin = () => {
    // Navigate to password login or show password input
    navigation.navigate('PasswordLogin');
  };

  const openDeviceSettings = () => {
    // Open device settings (requires additional setup)
    Linking.openSettings();
  };

  return (
    <TouchableOpacity onPress={handleBiometricLogin}>
      <Text>Login with {biometric.biometricTypeName}</Text>
    </TouchableOpacity>
  );
};
```

### Retry Logic with Exponential Backoff

```javascript
const BiometricRetryComponent = () => {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const { loginWithBiometric } = useAuth();

  const handleBiometricLoginWithRetry = async () => {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await loginWithBiometric();
        
        if (result.success) {
          setRetryCount(0);
          return result;
        }

        if (!result.canRetry || attempt === maxRetries) {
          throw new Error(result.error);
        }

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        setIsRetrying(true);
        await new Promise(resolve => setTimeout(resolve, delay));
        setIsRetrying(false);
        setRetryCount(attempt + 1);

      } catch (error) {
        if (attempt === maxRetries) {
          Alert.alert('Authentication Failed', 'Please use your password to login.');
          break;
        }
      }
    }
  };

  return (
    <View>
      <TouchableOpacity 
        onPress={handleBiometricLoginWithRetry}
        disabled={isRetrying}
      >
        <Text>
          {isRetrying ? `Retrying... (${retryCount}/3)` : 'Login with Biometric'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};
```

## 🔄 Integration with Navigation

### Protected Route with Biometric Check

```javascript
import React, { useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuth } from './AuthContext';

const Stack = createStackNavigator();

const ProtectedStack = () => {
  const { isAuthenticated, biometric, loginWithBiometric } = useAuth();

  useEffect(() => {
    // Auto-prompt for biometric if user returns to app
    const handleAppStateChange = (nextAppState) => {
      if (nextAppState === 'active' && isAuthenticated && biometric.isEnabled) {
        // Re-authenticate with biometric when app becomes active
        setTimeout(() => {
          loginWithBiometric();
        }, 500);
      }
    };

    AppState.addEventListener('change', handleAppStateChange);
    return () => AppState.removeEventListener('change', handleAppStateChange);
  }, [isAuthenticated, biometric.isEnabled]);

  return (
    <Stack.Navigator>
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="Settings" component={SettingsScreen} />
    </Stack.Navigator>
  );
};
```

## 📊 Analytics and Monitoring

### Biometric Usage Analytics

```javascript
const BiometricAnalytics = {
  trackBiometricEnabled: (biometricType) => {
    // Track when user enables biometric auth
    analytics.track('biometric_enabled', {
      type: biometricType,
      platform: Platform.OS,
      timestamp: Date.now(),
    });
  },

  trackBiometricLogin: (success, error = null) => {
    // Track biometric login attempts
    analytics.track('biometric_login_attempt', {
      success,
      error: error?.code || null,
      platform: Platform.OS,
      timestamp: Date.now(),
    });
  },

  trackBiometricFallback: (reason) => {
    // Track when users fall back to password
    analytics.track('biometric_fallback', {
      reason,
      platform: Platform.OS,
      timestamp: Date.now(),
    });
  },
};

// Usage in components
const result = await loginWithBiometric();
BiometricAnalytics.trackBiometricLogin(result.success, result.error);
```

This comprehensive biometric authentication system provides:

- ✅ **Cross-platform support** (iOS Face ID/Touch ID, Android Fingerprint/Face)
- ✅ **Secure credential storage** in device Keychain/Keystore
- ✅ **Automatic fallback** to password authentication
- ✅ **Comprehensive error handling** with user-friendly messages
- ✅ **Seamless integration** with existing authentication flow
- ✅ **Production-ready** security and user experience features

The system maintains full backward compatibility with password-only authentication while providing enhanced security and user experience through biometric authentication.
