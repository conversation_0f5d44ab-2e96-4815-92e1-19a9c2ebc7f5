# 🔐 Biometric Authentication Setup Guide

Complete installation and setup guide for React Native biometric authentication with automatic token refresh.

## 📦 Installation

### 1. Install Required Dependencies

```bash
# Core biometric libraries
npm install react-native-biometrics react-native-keychain

# If using Expo (managed workflow)
npx expo install react-native-biometrics react-native-keychain

# If using React Native CLI, you'll need to link native dependencies
cd ios && pod install && cd ..
```

### 2. Platform-Specific Setup

#### iOS Setup

1. **Add permissions to `ios/YourApp/Info.plist`:**
```xml
<key>NSFaceIDUsageDescription</key>
<string>Use Face ID to authenticate and access your account securely</string>
```

2. **Enable Keychain Sharing (Optional but recommended):**
   - Open your project in Xcode
   - Go to your target's "Signing & Capabilities"
   - Add "Keychain Sharing" capability
   - Add a keychain group (e.g., `group.com.yourapp.keychain`)

3. **Update minimum iOS version in `ios/Podfile`:**
```ruby
platform :ios, '11.0'  # Minimum for Face ID/Touch ID
```

#### Android Setup

1. **Add permissions to `android/app/src/main/AndroidManifest.xml`:**
```xml
<uses-permission android:name="android.permission.USE_FINGERPRINT" />
<uses-permission android:name="android.permission.USE_BIOMETRIC" />
```

2. **Update minimum SDK version in `android/app/build.gradle`:**
```gradle
android {
    compileSdkVersion 33
    
    defaultConfig {
        minSdkVersion 23  // Minimum for fingerprint
        targetSdkVersion 33
    }
}
```

3. **Add ProGuard rules (if using ProGuard) in `android/app/proguard-rules.pro`:**
```
-keep class com.rnbiometrics.** { *; }
-keep class androidx.biometric.** { *; }
```

## 🚀 Integration Steps

### 1. Update Your App.js

```javascript
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { AuthProvider } from './AuthContext';
import LoginScreen from './LoginScreen';
import HomeScreen from './HomeScreen';
import BiometricSettings from './BiometricSettings';

const Stack = createStackNavigator();

const App = () => {
  return (
    <AuthProvider>
      <NavigationContainer>
        <Stack.Navigator>
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="Home" component={HomeScreen} />
          <Stack.Screen 
            name="BiometricSettings" 
            component={BiometricSettings}
            options={{ title: 'Biometric Settings' }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </AuthProvider>
  );
};

export default App;
```

### 2. Update Your Backend API

Your backend should support these endpoints:

```javascript
// Login endpoint response
POST /auth/login
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  }
}

// Token refresh endpoint
POST /auth/refresh
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}

// Response
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..." // optional new refresh token
}
```

### 3. Configure API Base URL

Update the API_BASE_URL in:
- `AuthService.js`
- `SecureApiClient.js`

```javascript
const API_BASE_URL = 'https://your-api-domain.com/api';
```

## 🧪 Testing

### Testing on iOS Simulator

1. **Enable Face ID/Touch ID in Simulator:**
   - Device → Face ID/Touch ID → Enrolled
   - Features → Face ID/Touch ID → Matching Face/Finger

2. **Test Scenarios:**
   ```javascript
   // Test successful authentication
   Device → Face ID/Touch ID → Matching Face

   // Test failed authentication
   Device → Face ID/Touch ID → Non-matching Face

   // Test lockout
   Fail authentication 5 times consecutively
   ```

### Testing on Android Emulator

1. **Enable Fingerprint in Emulator:**
   - Settings → Security → Fingerprint
   - Add fingerprint using extended controls (⋯ button)

2. **Test with ADB Commands:**
   ```bash
   # Simulate successful fingerprint
   adb -e emu finger touch 1

   # Simulate failed fingerprint
   adb -e emu finger touch 2
   ```

### Testing on Physical Devices

1. **iOS Device:**
   - Ensure Face ID/Touch ID is set up in Settings
   - Test with enrolled and non-enrolled biometrics

2. **Android Device:**
   - Ensure fingerprint/face unlock is set up
   - Test with different biometric types

## 🔧 Configuration Options

### Customizing Biometric Prompts

```javascript
// In BiometricService.js
const { success, error } = await this.rnBiometrics.simplePrompt({
  promptMessage: 'Authenticate to access your account',
  cancelButtonText: 'Use Password',
  // iOS specific options
  fallbackPromptMessage: 'Use Passcode',
  // Android specific options
  subtitle: 'Use your biometric to authenticate',
  description: 'Place your finger on the sensor or look at the camera',
});
```

### Keychain Configuration

```javascript
// In SecureStorage.js
const keychainOptions = {
  service: 'com.yourapp.tokens',
  accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY_OR_DEVICE_PASSCODE,
  authenticatePrompt: 'Authenticate to access your tokens',
  showModal: true,
  kLocalizedFallbackTitle: 'Use Password', // iOS only
};
```

## 🐛 Troubleshooting

### Common Issues

1. **"Biometric authentication is not available"**
   - Check device has biometric hardware
   - Ensure biometrics are enrolled in device settings
   - Verify app permissions

2. **"No biometric authentication is enrolled"**
   - Guide user to device settings to enroll biometrics
   - Provide fallback to password authentication

3. **Keychain access errors**
   - Check iOS keychain sharing configuration
   - Verify Android keystore permissions
   - Handle keychain migration properly

4. **Build errors on iOS**
   ```bash
   cd ios && rm -rf Pods Podfile.lock && pod install
   ```

5. **Build errors on Android**
   ```bash
   cd android && ./gradlew clean && cd ..
   npx react-native run-android
   ```

### Debug Mode

Enable debug logging:

```javascript
// In BiometricService.js constructor
console.log('Biometric Service Debug Mode Enabled');

// In BiometricErrorHandler.js
static logError(error, context = 'BiometricAuth') {
  if (__DEV__) {
    console.error(`[${context}] Biometric Error:`, {
      platform: Platform.OS,
      error: error,
      code: error?.code,
      message: error?.message,
      timestamp: new Date().toISOString(),
    });
  }
}
```

## 🚀 Production Deployment

### Security Checklist

- [ ] Use HTTPS for all API endpoints
- [ ] Implement proper token expiry times
- [ ] Use Keychain/Keystore for sensitive data storage
- [ ] Implement certificate pinning
- [ ] Add proper error logging and monitoring
- [ ] Test on multiple device types and OS versions
- [ ] Implement proper session management
- [ ] Add rate limiting for authentication attempts

### Performance Optimization

- [ ] Implement lazy loading for biometric components
- [ ] Cache biometric availability status
- [ ] Optimize keychain access patterns
- [ ] Implement proper memory management

### App Store Guidelines

**iOS:**
- Clearly explain biometric usage in app description
- Handle biometric enrollment changes gracefully
- Provide alternative authentication methods

**Android:**
- Request biometric permissions appropriately
- Handle different biometric types (fingerprint, face, iris)
- Test on various Android versions and manufacturers

## 📱 User Experience Best Practices

1. **Onboarding:**
   - Explain benefits of biometric authentication
   - Show setup process clearly
   - Provide easy way to skip and enable later

2. **Error Handling:**
   - Show clear, actionable error messages
   - Provide fallback options
   - Guide users to device settings when needed

3. **Settings:**
   - Allow easy enable/disable of biometric auth
   - Show current biometric status clearly
   - Provide security information

4. **Accessibility:**
   - Support VoiceOver/TalkBack
   - Provide alternative authentication methods
   - Test with accessibility tools

## 🔄 Migration Guide

If upgrading from password-only authentication:

1. **Gradual Rollout:**
   - Make biometric authentication optional initially
   - Monitor adoption rates and error rates
   - Gradually encourage adoption

2. **Data Migration:**
   - Migrate existing tokens to secure storage
   - Handle users with existing sessions
   - Provide smooth transition experience

3. **Backward Compatibility:**
   - Support users who don't enable biometrics
   - Handle app updates gracefully
   - Maintain password authentication as fallback
